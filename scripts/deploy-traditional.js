const { ethers, network } = require("hardhat");

async function main() {
    console.log("🚀 Starting Netronlink Token Deployment...");
    console.log("Network:", network.name);
    console.log("Chain ID:", network.config.chainId);

    const [deployer] = await ethers.getSigners();
    console.log("Deploying from account:", deployer.address);

    const balance = await deployer.provider.getBalance(deployer.address);
    console.log("Account balance:", ethers.formatEther(balance), "ETH");

    // Token distribution recipients and amounts
    const recipients = [
        "******************************************",
        "******************************************",
        "******************************************",
        "******************************************",
        "******************************************",
        "******************************************",
        "******************************************",
    ]
    const amounts = [
        ethers.parseEther("********"), // 12%
        ethers.parseEther("********"), // 15%
        ethers.parseEther("5000000"), // 5%
        ethers.parseEther("********"), // 20%
        ethers.parseEther("********"), // 10%
        ethers.parseEther("********"), // 14%
        ethers.parseEther("4000000"), // 4%
    ]
    // Total: 60M tokens for recipients + 20M team tokens = 80M total (exactly max supply)

    console.log("\n📋 Deployment Configuration:");
    console.log("Recipients:", recipients.length);
    console.log("Total allocation:", ethers.formatEther(amounts.reduce((a, b) => a + b, 0n)), "NTL");

    // Deploy Implementation Contract
    console.log("\n🔨 Deploying Netronlink Implementation...");
    const NetronlinkFactory = await ethers.getContractFactory("Netronlink");
    const implementation = await NetronlinkFactory.deploy();
    await implementation.waitForDeployment();
    const implementationAddress = await implementation.getAddress();
    console.log("✅ Implementation deployed to:", implementationAddress);

    // Deploy Proxy Contract
    console.log("\n🔨 Deploying Proxy Contract...");
    const ProxyFactory = await ethers.getContractFactory("OwnedUpgradeabilityProxy");
    const proxy = await ProxyFactory.deploy();
    await proxy.waitForDeployment();
    const proxyAddress = await proxy.getAddress();
    console.log("✅ Proxy deployed to:", proxyAddress);

    // Prepare initialization data
    console.log("\n⚙️ Preparing initialization data...");
    const initData = NetronlinkFactory.interface.encodeFunctionData("initialize", [
        recipients,
        amounts,
        deployer.address
    ]);

    // Set implementation and initialize
    console.log("🔗 Setting implementation and initializing...");
    const tx = await proxy.upgradeToAndCall(implementationAddress, initData);
    await tx.wait();
    console.log("✅ Proxy initialized successfully!");

    // Get the proxied contract instance
    const netronlink = await ethers.getContractAt("Netronlink", proxyAddress);

    // Verify deployment
    console.log("\n🔍 Verifying deployment...");
    const name = await netronlink.name();
    const symbol = await netronlink.symbol();
    const totalSupply = await netronlink.totalSupply();
    const owner = await netronlink.owner();

    console.log("\n📊 Deployment Summary:");
    console.log("========================================");
    console.log("🏗️  Network:", network.name);
    console.log("🎯 Implementation:", implementationAddress);
    console.log("🎯 Proxy (Main Contract):", proxyAddress);
    console.log("📛 Token Name:", name);
    console.log("🔤 Token Symbol:", symbol);
    console.log("💰 Total Supply:", ethers.formatEther(totalSupply), "NTL");
    console.log("👤 Contract Owner:", owner);
    console.log("========================================");

    // Verify some recipient balances
    console.log("\n💰 Token Distribution Verification:");
    for (let i = 0; i < Math.min(3, recipients.length); i++) {
        const balance = await netronlink.balanceOf(recipients[i]);
        console.log(`  Recipient ${i + 1}: ${ethers.formatEther(balance)} NTL`);
    }

    console.log("\n🎉 Deployment completed successfully!");
    console.log("💡 Use proxy address for all interactions:", proxyAddress);

    // Schedule verification after 10 seconds
    console.log("\n⏳ Scheduling contract verification in 10 seconds...");
    setTimeout(async () => {
        try {
            console.log("\n🔍 Starting automatic contract verification...");
            const { spawn } = require('child_process');

            // Set environment variables for the verification script
            const env = {
                ...process.env,
                IMPLEMENTATION_ADDRESS: implementationAddress,
                PROXY_ADDRESS: proxyAddress
            };

            const verifyProcess = spawn('npx', [
                'hardhat', 'run', 'scripts/verify-deployed.js',
                '--network', network.name
            ], {
                stdio: 'inherit',
                cwd: process.cwd(),
                env: env
            });

            verifyProcess.on('close', (code) => {
                if (code === 0) {
                    console.log("✅ Automatic verification completed successfully!");
                } else {
                    console.log("⚠️ Automatic verification failed. You can run verification manually:");
                    console.log(`IMPLEMENTATION_ADDRESS=${implementationAddress} PROXY_ADDRESS=${proxyAddress} npx hardhat run scripts/verify-deployed.js --network ${network.name}`);
                }
            });

        } catch (error) {
            console.log("⚠️ Could not start automatic verification:", error.message);
            console.log("You can run verification manually:");
            console.log(`IMPLEMENTATION_ADDRESS=${implementationAddress} PROXY_ADDRESS=${proxyAddress} npx hardhat run scripts/verify-deployed.js --network ${network.name}`);
        }
    }, 10000);

    return {
        implementation: implementationAddress,
        proxy: proxyAddress,
        token: proxyAddress // Main contract address
    };
}

main()
    .then((result) => {
        console.log("\n✅ Deployment script completed successfully!");
        process.exit(0);
    })
    .catch((error) => {
        console.error("\n❌ Deployment failed:", error);
        process.exit(1);
    });
