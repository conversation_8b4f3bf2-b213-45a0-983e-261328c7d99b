const { ethers, run, network } = require("hardhat");

async function verifyContracts(implementationAddress, proxyAddress) {
    console.log("🔍 Starting contract verification...");
    console.log("Network:", network.name);
    console.log("📋 Using provided addresses");
    console.log("Implementation:", implementationAddress);
    console.log("Proxy:", proxyAddress);


    try {
        // Wait for contracts to be indexed
        console.log("⏳ Waiting for contracts to be indexed...");
        await new Promise(resolve => setTimeout(resolve, 30000)); // 30 second delay

        // Verify Implementation Contract
        console.log("\n🔨 Verifying Netronlink Implementation...");
        await run("verify:verify", {
            address: implementationAddress,
            constructorArguments: [],
            contract: "contracts/Netronlink.sol:Netronlink"
        });
        console.log("✅ Implementation contract verified!");

        // Verify Proxy Contract
        console.log("\n🔨 Verifying Proxy Contract...");
        await run("verify:verify", {
            address: proxyAddress,
            constructorArguments: [],
            contract: "contracts/OwnedUpgradeabilityProxy.sol:OwnedUpgradeabilityProxy"
        });
        console.log("✅ Proxy contract verified!");

        // Verify contract functionality
        console.log("\n🔍 Verifying contract functionality...");
        const netronlink = await ethers.getContractAt("Netronlink", proxyAddress);

        const name = await netronlink.name();
        const symbol = await netronlink.symbol();
        const totalSupply = await netronlink.totalSupply();
        const owner = await netronlink.owner();

        console.log("\n📊 Contract Information:");
        console.log("Token Name:", name);
        console.log("Token Symbol:", symbol);
        console.log("Total Supply:", ethers.formatEther(totalSupply), "NTL");
        console.log("Contract Owner:", owner);

        console.log("\n📄 Verification Summary:");
        console.log("========================================");
        console.log("🏗️  Network:", network.name);
        console.log("🎯 Implementation:", implementationAddress);
        console.log("🎯 Proxy (Main Contract):", proxyAddress);
        console.log("🔗 Sepolia Explorer: https://sepolia.etherscan.io");
        console.log("========================================");

        console.log("\n📋 Contract Links:");
        console.log("Main Contract:", `https://sepolia.etherscan.io/address/${proxyAddress}`);
        console.log("Implementation:", `https://sepolia.etherscan.io/address/${implementationAddress}`);

        console.log("\n✅ Verification completed successfully!");

    } catch (error) {
        console.error("❌ Verification failed:", error.message);

        if (error.message.includes("Already Verified")) {
            console.log("ℹ️  Contract was already verified");
        } else if (error.message.includes("does not have bytecode")) {
            console.error("❌ Contract address does not exist or has no bytecode");
        } else if (error.message.includes("Fail - Unable to verify")) {
            console.error("❌ Unable to verify contract. Check if the constructor arguments match deployment");
        }
    }
}

async function main() {
    // Get contract addresses from command line arguments, environment variables, or use defaults
    const args = process.argv.slice(2);
    let implementationAddress, proxyAddress;

    if (args.length >= 2) {
        implementationAddress = args[0];
        proxyAddress = args[1];
        console.log("📋 Using addresses from command line arguments");
    } else if (process.env.IMPLEMENTATION_ADDRESS && process.env.PROXY_ADDRESS) {
        implementationAddress = process.env.IMPLEMENTATION_ADDRESS;
        proxyAddress = process.env.PROXY_ADDRESS;
        console.log("📋 Using addresses from environment variables");
    } else {
        // Fallback to latest deployment addresses
        implementationAddress = "0x59A9894AA074c46a3a4BDDC150fB3Fc4bF2c6716";
        proxyAddress = "0x1857C24038fA5224A65554565390592Ce64ddBCD";
        console.log("📋 Using default addresses");
    }

    // Call the verification function
    await verifyContracts(implementationAddress, proxyAddress);
}

main()
    .then(() => {
        console.log("\n🎉 Verification script completed!");
        process.exit(0);
    })
    .catch((error) => {
        console.error("\n❌ Verification script failed:", error);
        process.exit(1);
    });

// Export the function for use in other scripts
module.exports = { verifyContracts };
