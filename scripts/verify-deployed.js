const { ethers, run, network } = require("hardhat");

// Rate limiting helper function
async function delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
}

// Verify with retry logic and rate limiting
async function verifyWithRetry(contractName, address, constructorArgs, contractPath, maxRetries = 3) {
    for (let i = 0; i < maxRetries; i++) {
        try {
            console.log(`\n🔨 Verifying ${contractName}... (Attempt ${i + 1}/${maxRetries})`);

            await run("verify:verify", {
                address: address,
                constructorArguments: constructorArgs,
                contract: contractPath
            });

            console.log(`✅ ${contractName} contract verified!`);
            return true;
        } catch (error) {
            if (error.message.includes("already been verified")) {
                console.log(`✅ ${contractName} contract was already verified!`);
                console.log(`https://${network.name === 'mainnet' ? '' : network.name + '.'}etherscan.io/address/${address}#code`);
                return true;
            } else if (error.message.includes("rate limit") || error.message.includes("Max calls per sec")) {
                console.log(`⚠️ Rate limit hit for ${contractName}. Waiting 10 seconds before retry...`);
                await delay(10000); // Wait 10 seconds
                continue;
            } else if (i === maxRetries - 1) {
                console.log(`❌ Verification failed for ${contractName}:`, error.message);
                return false;
            } else {
                console.log(`⚠️ Verification attempt ${i + 1} failed for ${contractName}. Retrying...`);
                await delay(5000); // Wait 5 seconds before retry
            }
        }
    }
    return false;
}

async function verifyContracts(implementationAddress, proxyAddress) {
    console.log("🔍 Starting contract verification...");
    console.log("Network:", network.name);
    console.log("📋 Using provided addresses");
    console.log("Implementation:", implementationAddress);
    console.log("Proxy:", proxyAddress);

    // Skip verification for local networks
    if (network.name === "hardhat" || network.name === "localhost") {
        console.log("⚠️ Skipping verification for local network:", network.name);
        console.log("✅ Verification script completed!");
        return;
    }

    try {
        // Wait for contracts to be indexed
        console.log("⏳ Waiting for contracts to be indexed...");
        await delay(30000); // 30 second delay

        // Verify Implementation Contract with rate limiting
        await verifyWithRetry(
            "Netronlink Implementation",
            implementationAddress,
            [],
            "contracts/Netronlink.sol:Netronlink"
        );

        // Wait between verifications to respect rate limits (increased to 15 seconds)
        console.log("⏳ Waiting 15 seconds before next verification...");
        await delay(15000);

        // Verify Proxy Contract with rate limiting
        await verifyWithRetry(
            "Proxy Contract",
            proxyAddress,
            [],
            "contracts/OwnedUpgradeabilityProxy.sol:OwnedUpgradeabilityProxy"
        );
        console.log("✅ Proxy contract verified!");

        // Verify contract functionality
        console.log("\n🔍 Verifying contract functionality...");
        const netronlink = await ethers.getContractAt("Netronlink", proxyAddress);

        const name = await netronlink.name();
        const symbol = await netronlink.symbol();
        const totalSupply = await netronlink.totalSupply();
        const owner = await netronlink.owner();

        console.log("\n📊 Contract Information:");
        console.log("Token Name:", name);
        console.log("Token Symbol:", symbol);
        console.log("Total Supply:", ethers.formatEther(totalSupply), "NTL");
        console.log("Contract Owner:", owner);

        console.log("\n📄 Verification Summary:");
        console.log("========================================");
        console.log("🏗️  Network:", network.name);
        console.log("🎯 Implementation:", implementationAddress);
        console.log("🎯 Proxy (Main Contract):", proxyAddress);
        console.log("🔗 Sepolia Explorer: https://sepolia.etherscan.io");
        console.log("========================================");

        console.log("\n📋 Contract Links:");
        console.log("Main Contract:", `https://sepolia.etherscan.io/address/${proxyAddress}`);
        console.log("Implementation:", `https://sepolia.etherscan.io/address/${implementationAddress}`);

        console.log("\n✅ Verification completed successfully!");

    } catch (error) {
        console.error("❌ Verification failed:", error.message);

        if (error.message.includes("Already Verified")) {
            console.log("ℹ️  Contract was already verified");
        } else if (error.message.includes("does not have bytecode")) {
            console.error("❌ Contract address does not exist or has no bytecode");
        } else if (error.message.includes("Fail - Unable to verify")) {
            console.error("❌ Unable to verify contract. Check if the constructor arguments match deployment");
        }
    }
}

async function main() {
    // Get contract addresses from command line arguments, environment variables, or use defaults
    const args = process.argv.slice(2);
    let implementationAddress, proxyAddress;
    console.log(args, "args")
    if (args.length >= 2) {
        implementationAddress = args[0];
        proxyAddress = args[1];
        console.log("📋 Using addresses from command line arguments");
    } else if (process.env.IMPLEMENTATION_ADDRESS && process.env.PROXY_ADDRESS) {
        implementationAddress = process.env.IMPLEMENTATION_ADDRESS;
        proxyAddress = process.env.PROXY_ADDRESS;
        console.log("📋 Using addresses from environment variables");
    } else {
        // Fallback to latest deployment addresses
        implementationAddress = "0x59A9894AA074c46a3a4BDDC150fB3Fc4bF2c6716";
        proxyAddress = "0x1857C24038fA5224A65554565390592Ce64ddBCD";
        console.log("📋 Using default addresses");
    }

    // Call the verification function
    await verifyContracts(implementationAddress, proxyAddress);
}

main()
    .then(() => {
        console.log("\n🎉 Verification script completed!");
        process.exit(0);
    })
    .catch((error) => {
        console.error("\n❌ Verification script failed:", error);
        process.exit(1);
    });

// Export the function for use in other scripts
module.exports = { verifyContracts };
